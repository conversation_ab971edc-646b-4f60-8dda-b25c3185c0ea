import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, Star, TrendingUp, Grid3X3 } from 'lucide-react';
import { StoreAgent } from '../types';
import { StoreAgentCard } from './StoreAgentCard';
import { StoreBanner } from './StoreBanner';
import { useStoreAgents } from '../hooks/useStoreAgents';

interface StoreViewProps {}

// 分类配置
const storeCategories = [
  { id: 'featured', name: '精选应用', icon: '⭐' },
  { id: '智慧教学', name: '智慧教学', icon: '🎓' },
  { id: '教师助手', name: '教师助手', icon: '👨‍🏫' },
  { id: '学习助手', name: '学习助手', icon: '📚' },
  { id: '角色模拟', name: '角色模拟', icon: '🎭' },
  { id: '语文', name: '语文', icon: '📝' },
  { id: '数学', name: '数学', icon: '🔢' },
  { id: '英语', name: '英语', icon: '🌍' },
];

export const StoreView: React.FC<StoreViewProps> = () => {
  const [activeCategory, setActiveCategory] = useState('featured');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'followers' | 'name' | 'newest'>('followers');
  const { agents, getAgentsByCategory, searchAgents } = useStoreAgents();

  const filteredAndSortedAgents = useMemo(() => {
    let result = searchTerm
      ? searchAgents(searchTerm, activeCategory === 'featured' ? undefined : activeCategory)
      : getAgentsByCategory(activeCategory);

    // 排序
    result = [...result].sort((a, b) => {
      switch (sortBy) {
        case 'followers':
          return b.followers - a.followers;
        case 'name':
          return a.name.localeCompare(b.name);
        case 'newest':
          return b.id.localeCompare(a.id); // 简单按ID排序模拟最新
        default:
          return 0;
      }
    });

    return result;
  }, [agents, activeCategory, searchTerm, sortBy, getAgentsByCategory, searchAgents]);

  const getSectionTitle = () => {
    const titles: Record<string, string> = {
      'featured': '精选应用',
      '智慧教学': '智慧教学',
      '教师助手': '教师助手',
      '学习助手': '学习助手',
      '角色模拟': '角色模拟',
      '语文': '语文',
      '数学': '数学',
      '英语': '英语',
    };
    return titles[activeCategory] || '智能体商店';
  };

  const getSectionIcon = () => {
    switch (activeCategory) {
      case 'featured':
        return <Star className="text-yellow-500" size={28} />;
      case 'trending':
        return <TrendingUp className="text-green-500" size={28} />;
      default:
        return <Grid3X3 className="text-blue-500" size={28} />;
    }
  };

  const getSectionDescription = () => {
    const descriptions: Record<string, string> = {
      'featured': '发现最受欢迎的教育智能体',
      '智慧教学': '提升教学效率的智能助手',
      '教师助手': '专业的教学辅助工具',
      '学习助手': '个性化学习支持系统',
      '角色模拟': '沉浸式角色扮演学习',
      '语文': '语文学科专业辅导',
      '数学': '数学思维训练专家',
      '英语': '英语学习全方位支持',
    };
    return descriptions[activeCategory] || '探索教育场景的智能体应用';
  };

  return (
    <div className="flex-1 p-8 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div 
          className="flex items-center justify-between mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center space-x-4">
            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl flex items-center justify-center shadow-lg"
              whileHover={{ scale: 1.05, rotate: 5 }}
            >
              {getSectionIcon()}
            </motion.div>
            <div>
              <motion.h1 
                className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-blue-800 to-purple-800 bg-clip-text text-transparent"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                {getSectionTitle()}
              </motion.h1>
              <motion.p 
                className="text-slate-600 mt-2 text-lg"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {getSectionDescription()}
              </motion.p>
            </div>
          </div>
        </motion.div>

        {/* Banner Carousel */}
        <StoreBanner className="mb-8" />

        {/* Category Tabs */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-2 shadow-lg">
            <div className="flex flex-wrap gap-2">
              {storeCategories.map((category, index) => (
                <motion.button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`
                    relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 flex items-center space-x-2
                    ${activeCategory === category.id
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/25'
                      : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
                    }
                  `}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 + index * 0.05, duration: 0.4 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {/* Background gradient for active state */}
                  {activeCategory === category.id && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl"
                      layoutId="activeCategoryTab"
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}

                  <span className="relative z-10 text-lg">{category.icon}</span>
                  <span className="relative z-10">{category.name}</span>
                </motion.button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          className="flex items-center space-x-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.6 }}
        >
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <motion.input
              type="text"
              placeholder="搜索智能体名称、描述或标签"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-slate-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm"
              whileFocus={{ scale: 1.02 }}
            />
          </div>
          
          <div className="flex items-center space-x-3">
            <Filter className="text-slate-400" size={20} />
            <motion.select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'followers' | 'name' | 'newest')}
              className="px-4 py-3 border border-slate-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm"
              whileFocus={{ scale: 1.02 }}
            >
              <option value="followers">按热度排序</option>
              <option value="name">按名称排序</option>
              <option value="newest">按最新排序</option>
            </motion.select>
          </div>
        </motion.div>

        {/* Stats */}
        <motion.div
          className="flex items-center space-x-6 mb-8 text-sm text-slate-600"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0, duration: 0.6 }}
        >
          <span>共找到 <strong className="text-slate-900">{filteredAndSortedAgents.length}</strong> 个智能体</span>
          {searchTerm && (
            <span>搜索关键词: <strong className="text-blue-600">"{searchTerm}"</strong></span>
          )}
        </motion.div>

        {/* Agents Grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          initial="hidden"
          animate="visible"
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.05
              }
            }
          }}
        >
          {filteredAndSortedAgents.map((agent, index) => (
            <StoreAgentCard 
              key={agent.id} 
              agent={agent} 
              index={index}
            />
          ))}
        </motion.div>

        {/* Empty State */}
        {filteredAndSortedAgents.length === 0 && (
          <motion.div 
            className="text-center py-16"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div 
              className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg"
              animate={{ 
                rotate: [0, 5, -5, 0],
                scale: [1, 1.05, 1]
              }}
              transition={{ 
                duration: 4, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Search className="text-blue-500" size={32} />
            </motion.div>
            <h3 className="text-2xl font-bold text-slate-900 mb-3">没有找到相关智能体</h3>
            <p className="text-slate-600 mb-6 text-lg">
              {searchTerm 
                ? `尝试使用其他关键词搜索，或浏览其他分类`
                : `该分类下暂无智能体，敬请期待更多内容`
              }
            </p>
            {searchTerm && (
              <motion.button
                onClick={() => setSearchTerm('')}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-2xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-semibold shadow-lg shadow-blue-500/25"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                清除搜索条件
              </motion.button>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
};