import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Eye, EyeOff, MoreHorizontal, Edit } from 'lucide-react';
import { AIApplication } from '../types';

interface AIApplicationCardProps {
  application: AIApplication;
  index: number;
  onEdit?: (applicationId: string) => void;
}

export const AIApplicationCard: React.FC<AIApplicationCardProps> = ({ 
  application, 
  index, 
  onEdit 
}) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const handleCardClick = () => {
    if (onEdit) {
      onEdit(application.id);
    }
  };

  return (
    <motion.div
      className="group relative bg-white rounded-2xl border border-slate-200/60 p-6 hover:shadow-2xl transition-all duration-500 hover:border-blue-300/50 overflow-hidden cursor-pointer"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.5, 
        delay: index * 0.1,
        ease: "easeOut"
      }}
      whileHover={{ 
        y: -8,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
      whileTap={{ scale: 0.98 }}
      onClick={handleCardClick}
    >
      {/* Background gradient overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        initial={false}
      />
      
      {/* Animated border */}
      <motion.div
        className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
        style={{
          background: 'linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%)',
          backgroundSize: '200% 200%',
        }}
        animate={{
          backgroundPosition: ['0% 0%', '100% 100%'],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatType: 'reverse',
        }}
      />

      <div className="relative z-10 flex items-start space-x-4">
        <motion.div 
          className="w-12 h-12 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg"
          whileHover={{ 
            scale: 1.1, 
            rotate: 5,
            boxShadow: "0 20px 25px -5px rgba(59, 130, 246, 0.4)"
          }}
          transition={{ type: "spring", stiffness: 400, damping: 17 }}
        >
          {application.name.charAt(0)}
        </motion.div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <motion.h3 
                className="font-semibold text-slate-900 truncate group-hover:text-blue-700 transition-colors duration-300"
                initial={{ opacity: 0.8 }}
                whileHover={{ opacity: 1 }}
              >
                {application.name}
              </motion.h3>
              <motion.p 
                className="text-sm text-slate-600 mt-2 line-clamp-2 leading-relaxed"
                initial={{ opacity: 0.7 }}
                whileHover={{ opacity: 1 }}
              >
                {application.description}
              </motion.p>
            </div>
            
            <motion.button
              className="opacity-0 group-hover:opacity-100 p-2 hover:bg-slate-100 rounded-lg transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={(e) => {
                e.stopPropagation();
                // Handle more options
              }}
            >
              <MoreHorizontal size={16} className="text-slate-400" />
            </motion.button>
          </div>
          
          <div className="flex items-center justify-between mt-4">
            <motion.button
              className={`
                px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-300 flex items-center space-x-1.5
                ${application.isPublished
                  ? 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 hover:from-emerald-200 hover:to-green-200 shadow-sm'
                  : 'bg-gradient-to-r from-slate-100 to-gray-100 text-slate-600 hover:from-slate-200 hover:to-gray-200'
                }
              `}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={(e) => e.stopPropagation()}
            >
              {application.isPublished ? (
                <>
                  <Eye size={12} />
                  <span>已发布</span>
                </>
              ) : (
                <>
                  <EyeOff size={12} />
                  <span>未发布</span>
                </>
              )}
            </motion.button>
            
            <div className="flex items-center text-xs text-slate-500">
              <Calendar size={12} className="mr-1.5" />
              <span>{formatDate(application.lastEdited)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Edit overlay on hover */}
      <motion.div
        className="absolute inset-0 bg-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center"
        initial={false}
      >
        <motion.div
          className="bg-white/90 backdrop-blur-sm rounded-xl px-4 py-2 flex items-center space-x-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={{ scale: 0.8 }}
          whileHover={{ scale: 1 }}
        >
          <Edit size={16} className="text-blue-600" />
          <span className="text-sm font-medium text-blue-700">编辑应用</span>
        </motion.div>
      </motion.div>

      {/* Subtle shine effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 opacity-0 group-hover:opacity-100"
        initial={{ x: '-100%' }}
        whileHover={{ x: '200%' }}
        transition={{ duration: 0.8, ease: "easeInOut" }}
      />
    </motion.div>
  );
};