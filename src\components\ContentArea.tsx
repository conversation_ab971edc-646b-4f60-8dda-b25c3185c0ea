import React from 'react';
import { motion } from 'framer-motion';
import { AIApplication, Resource } from '../types';
import { AIApplicationsView } from './AIApplicationsView';
import { ResourceLibraryView } from './ResourceLibraryView';
import { StoreView } from './StoreView';
import { WalletBalanceView } from './WalletBalanceView';

interface ContentAreaProps {
  activeSection: string;
  activeSubSection: string;
  applications: AIApplication[];
  resources: Resource[];
  onCreateApplication: (data: { name: string; description: string; avatar: string }) => string;
  onEditApplication: (applicationId: string) => void;
  onCreateResource: (data: Partial<Resource>) => string;
  onDeleteResource: (id: string) => void;
  onUpdateResource: (id: string, updates: Partial<Resource>) => void;
}

export const ContentArea: React.FC<ContentAreaProps> = ({
  activeSection,
  activeSubSection,
  applications,
  resources,
  onCreateApplication,
  onEditApplication,
  onCreateResource,
  onDeleteResource,
  onUpdateResource,
}) => {
  const renderContent = () => {
    if (activeSection === 'studio' && activeSubSection === 'ai-apps') {
      return (
        <AIApplicationsView
          applications={applications}
          onCreateApplication={onCreateApplication}
          onEditApplication={onEditApplication}
        />
      );
    }

    if (activeSection === 'studio' && activeSubSection === 'resources') {
      return (
        <ResourceLibraryView
          resources={resources}
          onCreateResource={onCreateResource}
          onDeleteResource={onDeleteResource}
          onUpdateResource={onUpdateResource}
        />
      );
    }

    if (activeSection === 'store') {
      return <StoreView activeSubSection={activeSubSection} />;
    }

    if (activeSection === 'wallet' && activeSubSection === 'balance') {
      return <WalletBalanceView />;
    }

    // Placeholder for other sections
    return (
      <motion.div 
        className="flex-1 p-8 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 min-h-screen"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-7xl mx-auto">
          <motion.div 
            className="bg-white/80 backdrop-blur-sm rounded-3xl border border-slate-200/60 p-12 text-center shadow-xl"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"
              animate={{ 
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <div className="w-6 h-6 bg-white rounded-lg opacity-90"></div>
            </motion.div>
            
            <motion.h2 
              className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              {getSectionTitle(activeSection, activeSubSection)}
            </motion.h2>
            
            <motion.p 
              className="text-slate-600 text-lg"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
            >
              此功能正在开发中，敬请期待...
            </motion.p>
            
            <motion.div
              className="mt-8 w-32 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto"
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            />
          </motion.div>
        </div>
      </motion.div>
    );
  };

  return renderContent();
};

const getSectionTitle = (section: string, subSection: string): string => {
  const titles: Record<string, Record<string, string>> = {
    studio: {
      'ai-apps': 'AI 应用',
      'resources': '资源库',
      'templates': '模板',
      'evaluation': '评测',
    },
    store: {
      'featured': '精选应用',
      'categories': '分类浏览',
      'trending': '热门应用',
    },
    wallet: {
      'balance': '余额',
      'transactions': '交易记录',
      'billing': '账单',
    },
    // Add more sections as needed
  };

  return titles[section]?.[subSection] || '页面';
};