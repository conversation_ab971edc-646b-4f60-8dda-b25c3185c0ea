import { useState } from 'react';
import { Resource, KnowledgeBase, Plugin } from '../types';

const generateMockResources = (): Resource[] => {
  const mockResources: Resource[] = [
    {
      id: '1',
      name: 'test',
      description: '测试知识库',
      type: 'knowledge',
      category: '知识库',
      lastModified: new Date('2025-06-30T11:04:00'),
      status: 'active',
      documentCount: 15,
      totalSize: '2.3MB',
      sources: ['PDF', 'TXT', 'DOCX'],
      fileCount: 15,
    } as KnowledgeBase,
    {
      id: '2',
      name: '听评课记录',
      description: '教育评估工具',
      type: 'plugin',
      category: '插件',
      lastModified: new Date('2025-04-27T13:31:00'),
      status: 'active',
      version: '1.2.0',
      author: 'EduTech',
      downloads: 1250,
      rating: 4.8,
    } as Plugin,
    {
      id: '3',
      name: 'txt2img',
      description: '文字转图片工具',
      type: 'plugin',
      category: '插件',
      lastModified: new Date('2025-04-04T23:31:00'),
      status: 'active',
      version: '2.1.5',
      author: 'ImageAI',
      downloads: 3420,
      rating: 4.6,
    } as Plugin,
    {
      id: '4',
      name: 'notebookim',
      description: '文本转语音技术库',
      type: 'knowledge',
      category: '知识库',
      lastModified: new Date('2025-04-02T13:55:00'),
      status: 'active',
      documentCount: 28,
      totalSize: '5.7MB',
      sources: ['PDF', 'MD', 'TXT'],
      fileCount: 28,
    } as KnowledgeBase,
    {
      id: '5',
      name: 'pic_book12_1_177',
      description: '海洋生物图鉴数据集',
      type: 'knowledge',
      category: '知识库',
      lastModified: new Date('2025-03-27T20:11:00'),
      status: 'processing',
      documentCount: 177,
      totalSize: '45.2MB',
      sources: ['PDF', 'JPG', 'PNG'],
      fileCount: 177,
    } as KnowledgeBase,
    {
      id: '6',
      name: 'pic_book345_1_780',
      description: '综合图书数据库',
      type: 'knowledge',
      category: '知识库',
      lastModified: new Date('2025-03-27T20:11:00'),
      status: 'active',
      documentCount: 780,
      totalSize: '128.5MB',
      sources: ['PDF', 'EPUB', 'TXT'],
      fileCount: 780,
    } as KnowledgeBase,
  ];

  return mockResources;
};

export const useResources = () => {
  const [resources, setResources] = useState<Resource[]>(generateMockResources());

  const createResource = (data: Partial<Resource>): string => {
    const newResource: Resource = {
      id: Date.now().toString(),
      name: data.name || '',
      description: data.description || '',
      type: data.type || 'knowledge',
      category: data.type === 'knowledge' ? '知识库' : '插件',
      lastModified: new Date(),
      status: 'processing',
      ...data,
    };

    setResources((prev) => [newResource, ...prev]);
    return newResource.id;
  };

  const deleteResource = (id: string) => {
    setResources((prev) => prev.filter(resource => resource.id !== id));
  };

  const updateResource = (id: string, updates: Partial<Resource>) => {
    setResources((prev) => 
      prev.map(resource => 
        resource.id === id 
          ? { ...resource, ...updates, lastModified: new Date() }
          : resource
      )
    );
  };

  return {
    resources,
    createResource,
    deleteResource,
    updateResource,
  };
};