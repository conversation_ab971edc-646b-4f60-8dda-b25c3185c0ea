import React from 'react';
import { motion } from 'framer-motion';
import { 
  Home, 
  Store, 
  FileText, 
  Wallet, 
  User 
} from 'lucide-react';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const navigationItems = [
  { id: 'studio', name: '工作室', icon: Home },
  { id: 'store', name: '商店', icon: Store },
  { id: 'templates', name: '模板', icon: FileText },
  { id: 'wallet', name: '钱包', icon: Wallet },
  { id: 'my', name: '我的', icon: User },
];

export const Sidebar: React.FC<SidebarProps> = ({ activeSection, onSectionChange }) => {
  return (
    <motion.div 
      className="w-20 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 flex flex-col items-center py-6 space-y-4 shadow-2xl border-r border-slate-700/50"
      initial={{ x: -100, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      {/* Logo Area */}
      <motion.div 
        className="w-12 h-12 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg"
        whileHover={{ scale: 1.1, rotate: 5 }}
        whileTap={{ scale: 0.95 }}
      >
        <div className="w-6 h-6 bg-white rounded-lg opacity-90"></div>
      </motion.div>

      {navigationItems.map((item, index) => {
        const IconComponent = item.icon;
        const isActive = activeSection === item.id;
        
        return (
          <motion.button
            key={item.id}
            onClick={() => onSectionChange(item.id)}
            className="flex flex-col items-center space-y-1 group"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1, duration: 0.5 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Icon Container */}
            <div
              className={`
                relative w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-300
                ${isActive 
                  ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-xl shadow-blue-500/25' 
                  : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                }
              `}
            >
              {isActive && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl"
                  layoutId="activeTab"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
              <IconComponent size={20} className="relative z-10" />
              
              {/* Hover glow effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                initial={false}
              />
            </div>

            {/* Text Label */}
            <motion.span
              className={`
                text-xs font-medium transition-all duration-300 text-center leading-tight
                ${isActive 
                  ? 'text-blue-400' 
                  : 'text-slate-500 group-hover:text-slate-300'
                }
              `}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 + 0.2, duration: 0.5 }}
            >
              {item.name}
            </motion.span>
          </motion.button>
        );
      })}

      {/* Bottom decoration */}
      <motion.div 
        className="mt-auto w-8 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
        initial={{ scaleX: 0 }}
        animate={{ scaleX: 1 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      />
    </motion.div>
  );
};