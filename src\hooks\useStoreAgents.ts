import { useState } from 'react';
import { StoreAgent } from '../types';

const generateMockStoreAgents = (): StoreAgent[] => {
  return [
    // 智慧教学分类
    {
      id: '1',
      name: '微课脚本专家',
      description: '信息化教学支持工具，协助构建精品微课内容...',
      avatar: '🎬',
      category: '智慧教学',
      followers: 1700,
      author: '@Classin',
      tags: ['教学设计', '微课制作', '脚本编写'],
      isOfficial: true,
      featured: true,
    },
    {
      id: '2',
      name: '语文作文批阅',
      description: '我是语文作文专业改家，专注于中小学阶段多维...',
      avatar: '📝',
      category: '智慧教学',
      followers: 641,
      author: '@Classin',
      tags: ['语文教学', '作文批改', '写作指导'],
      isOfficial: true,
    },
    {
      id: '3',
      name: '问答题批阅',
      description: '智能分析学生回答题作答内容，评估知识点掌...',
      avatar: '❓',
      category: '智慧教学',
      followers: 191,
      author: '@Classin',
      tags: ['智能批阅', '问答分析', '知识评估'],
      isOfficial: true,
    },
    {
      id: '4',
      name: '教案设计',
      description: '数学蓝图智能规划师，提供学科核心素养导向...',
      avatar: '📋',
      category: '智慧教学',
      followers: 155,
      author: '@Classin',
      tags: ['教案设计', '教学规划', '核心素养'],
      isOfficial: true,
    },
    {
      id: '5',
      name: '公告通知',
      description: '我是写公告的高手，专门撰写新颖深入「清晰友...',
      avatar: '📢',
      category: '智慧教学',
      followers: 89,
      author: '@Classin',
      tags: ['公告写作', '通知发布', '文案编写'],
      isOfficial: true,
    },

    // 教师助手分类
    {
      id: '6',
      name: '英语作文批阅',
      description: '我是你的英语作文专属导师，专注为中小学阶...',
      avatar: '🇬🇧',
      category: '教师助手',
      followers: 786,
      author: '@Classin',
      tags: ['英语教学', '作文批改', '语言学习'],
      isOfficial: true,
      featured: true,
    },
    {
      id: '7',
      name: '数学大题批阅',
      description: '精准识别数学大题的解题步骤，逐步与结果，...',
      avatar: '🔢',
      category: '教师助手',
      followers: 307,
      author: '@Classin',
      tags: ['数学教学', '解题分析', '步骤批改'],
      isOfficial: true,
    },
    {
      id: '8',
      name: '创意作业设计',
      description: '专注于生成独特，激发学生兴趣的创意作业方...',
      avatar: '💡',
      category: '教师助手',
      followers: 161,
      author: '@Classin',
      tags: ['作业设计', '创意教学', '学习兴趣'],
      isOfficial: true,
    },
    {
      id: '9',
      name: '项目式学习',
      description: '跨学科课程设计引擎，提供真实情境任务设计...',
      avatar: '🎯',
      category: '教师助手',
      followers: 125,
      author: '@Classin',
      tags: ['项目学习', '跨学科', '任务设计'],
      isOfficial: true,
    },
    {
      id: '10',
      name: '情景化作业设计',
      description: '将学习内容融入真实生活或虚拟情境，设计实...',
      avatar: '🎭',
      category: '教师助手',
      followers: 98,
      author: '@Classin',
      tags: ['情景教学', '生活化学习', '实践应用'],
      isOfficial: true,
    },

    // 学习助手分类
    {
      id: '11',
      name: '学习计划制定师',
      description: '根据学生个人情况制定科学合理的学习计划...',
      avatar: '📅',
      category: '学习助手',
      followers: 523,
      author: '@Classin',
      tags: ['学习规划', '时间管理', '个性化'],
      isOfficial: true,
    },
    {
      id: '12',
      name: '错题分析专家',
      description: '深度分析学生错题原因，提供针对性改进建议...',
      avatar: '🔍',
      category: '学习助手',
      followers: 412,
      author: '@Classin',
      tags: ['错题分析', '学习诊断', '提升建议'],
      isOfficial: true,
    },
    {
      id: '13',
      name: '知识点串联师',
      description: '帮助学生建立知识点之间的联系，构建知识网络...',
      avatar: '🕸️',
      category: '学习助手',
      followers: 298,
      author: '@Classin',
      tags: ['知识串联', '思维导图', '系统学习'],
      isOfficial: true,
    },

    // 角色模拟分类
    {
      id: '14',
      name: '历史人物对话',
      description: '与历史名人进行跨时空对话，深度了解历史文化...',
      avatar: '👑',
      category: '角色模拟',
      followers: 445,
      author: '@Classin',
      tags: ['历史教学', '角色扮演', '文化传承'],
      isOfficial: true,
    },
    {
      id: '15',
      name: '科学家实验室',
      description: '模拟著名科学家，引导学生进行科学探索和实验...',
      avatar: '🔬',
      category: '角色模拟',
      followers: 367,
      author: '@Classin',
      tags: ['科学教育', '实验指导', '探索精神'],
      isOfficial: true,
    },
    {
      id: '16',
      name: '文学作家工作室',
      description: '化身文学大师，指导学生创作和文学鉴赏...',
      avatar: '✍️',
      category: '角色模拟',
      followers: 289,
      author: '@Classin',
      tags: ['文学创作', '写作指导', '文学鉴赏'],
      isOfficial: true,
    },

    // 语文分类
    {
      id: '17',
      name: '古诗词鉴赏师',
      description: '深度解析古诗词的意境、技法和文化内涵...',
      avatar: '🏮',
      category: '语文',
      followers: 612,
      author: '@Classin',
      tags: ['古诗词', '文学鉴赏', '传统文化'],
      isOfficial: true,
    },
    {
      id: '18',
      name: '阅读理解专家',
      description: '提升学生阅读理解能力，培养深度思考习惯...',
      avatar: '📖',
      category: '语文',
      followers: 534,
      author: '@Classin',
      tags: ['阅读理解', '思维训练', '语言文字'],
      isOfficial: true,
    },
    {
      id: '19',
      name: '作文素材库',
      description: '提供丰富的作文素材和写作技巧指导...',
      avatar: '📚',
      category: '语文',
      followers: 478,
      author: '@Classin',
      tags: ['作文素材', '写作技巧', '语言表达'],
      isOfficial: true,
    },

    // 数学分类
    {
      id: '20',
      name: '数学思维训练师',
      description: '培养学生数学逻辑思维和解题能力...',
      avatar: '🧮',
      category: '数学',
      followers: 689,
      author: '@Classin',
      tags: ['数学思维', '逻辑训练', '解题技巧'],
      isOfficial: true,
    },
    {
      id: '21',
      name: '几何图形专家',
      description: '帮助学生理解几何概念，提升空间想象力...',
      avatar: '📐',
      category: '数学',
      followers: 423,
      author: '@Classin',
      tags: ['几何学习', '空间思维', '图形理解'],
      isOfficial: true,
    },
    {
      id: '22',
      name: '应用题解析师',
      description: '将复杂应用题分解为简单步骤，培养解题思路...',
      avatar: '🔢',
      category: '数学',
      followers: 356,
      author: '@Classin',
      tags: ['应用题', '解题步骤', '数学应用'],
      isOfficial: true,
    },

    // 英语分类
    {
      id: '23',
      name: '英语口语练习伙伴',
      description: '提供真实的英语对话环境，提升口语表达能力...',
      avatar: '🗣️',
      category: '英语',
      followers: 756,
      author: '@Classin',
      tags: ['口语练习', '对话训练', '语言交流'],
      isOfficial: true,
    },
    {
      id: '24',
      name: '英语语法导师',
      description: '系统讲解英语语法规则，提供练习和纠错...',
      avatar: '📝',
      category: '英语',
      followers: 645,
      author: '@Classin',
      tags: ['英语语法', '规则讲解', '语言规范'],
      isOfficial: true,
    },
    {
      id: '25',
      name: '英语阅读理解',
      description: '提升英语阅读能力，培养语言感知和理解力...',
      avatar: '📰',
      category: '英语',
      followers: 512,
      author: '@Classin',
      tags: ['英语阅读', '理解能力', '语言感知'],
      isOfficial: true,
    },
  ];
};

export const useStoreAgents = () => {
  const [agents] = useState<StoreAgent[]>(generateMockStoreAgents());

  const getAgentsByCategory = (category: string) => {
    if (category === 'featured') {
      return agents.filter(agent => agent.featured);
    }
    return agents.filter(agent => agent.category === category);
  };

  const searchAgents = (query: string, category?: string) => {
    let filteredAgents = category && category !== 'featured' 
      ? agents.filter(agent => agent.category === category)
      : agents;

    if (query.trim()) {
      filteredAgents = filteredAgents.filter(agent =>
        agent.name.toLowerCase().includes(query.toLowerCase()) ||
        agent.description.toLowerCase().includes(query.toLowerCase()) ||
        agent.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      );
    }

    return filteredAgents;
  };

  return {
    agents,
    getAgentsByCategory,
    searchAgents,
  };
};