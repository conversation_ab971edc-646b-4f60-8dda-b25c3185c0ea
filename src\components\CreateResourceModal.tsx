import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Upload, Database, Puzzle, FileText, Globe } from 'lucide-react';
import { Resource } from '../types';

interface CreateResourceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<Resource>) => string;
}

export const CreateResourceModal: React.FC<CreateResourceModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'knowledge' as 'knowledge' | 'plugin',
    category: '知识库',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    setFormData({ name: '', description: '', type: 'knowledge', category: '知识库' });
    onClose();
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleTypeChange = (type: 'knowledge' | 'plugin') => {
    setFormData({ 
      ...formData, 
      type, 
      category: type === 'knowledge' ? '知识库' : '插件' 
    });
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div 
          className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={handleOverlayClick}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div 
            className="bg-white rounded-3xl w-full max-w-md shadow-2xl overflow-hidden"
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ 
              type: "spring", 
              damping: 25, 
              stiffness: 300,
              duration: 0.5 
            }}
          >
            {/* Header */}
            <div className="relative bg-gradient-to-r from-green-600 via-blue-700 to-purple-600 p-6 text-white">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-green-400/20 to-purple-400/20"
                animate={{
                  background: [
                    'linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(147, 51, 234, 0.2))',
                    'linear-gradient(45deg, rgba(147, 51, 234, 0.2), rgba(34, 197, 94, 0.2))',
                  ],
                }}
                transition={{ duration: 3, repeat: Infinity, repeatType: 'reverse' }}
              />
              
              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <motion.div
                    className="w-10 h-10 bg-white/20 rounded-2xl flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                  >
                    {formData.type === 'knowledge' ? <Database size={20} /> : <Puzzle size={20} />}
                  </motion.div>
                  <div>
                    <h2 className="text-xl font-bold">添加资源</h2>
                    <p className="text-blue-100 text-sm">创建知识库或插件</p>
                  </div>
                </div>
                
                <motion.button
                  onClick={onClose}
                  className="p-2 hover:bg-white/20 rounded-xl transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <X size={20} />
                </motion.button>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Type Selection */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <label className="block text-sm font-semibold text-slate-700 mb-3">
                  资源类型
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <motion.button
                    type="button"
                    onClick={() => handleTypeChange('knowledge')}
                    className={`p-4 rounded-xl border-2 transition-all duration-300 flex flex-col items-center space-y-2 ${
                      formData.type === 'knowledge'
                        ? 'border-green-500 bg-green-50 text-green-700'
                        : 'border-slate-200 hover:border-green-300 hover:bg-green-50'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Database size={24} />
                    <span className="font-medium">知识库</span>
                  </motion.button>
                  
                  <motion.button
                    type="button"
                    onClick={() => handleTypeChange('plugin')}
                    className={`p-4 rounded-xl border-2 transition-all duration-300 flex flex-col items-center space-y-2 ${
                      formData.type === 'plugin'
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-slate-200 hover:border-purple-300 hover:bg-purple-50'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Puzzle size={24} />
                    <span className="font-medium">插件</span>
                  </motion.button>
                </div>
              </motion.div>

              {/* Name Input */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <label htmlFor="name" className="block text-sm font-semibold text-slate-700 mb-2">
                  资源名称
                </label>
                <motion.input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 bg-slate-50 focus:bg-white"
                  placeholder={`请输入${formData.type === 'knowledge' ? '知识库' : '插件'}名称`}
                  required
                  whileFocus={{ scale: 1.02 }}
                />
              </motion.div>

              {/* Description Input */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <label htmlFor="description" className="block text-sm font-semibold text-slate-700 mb-2">
                  资源描述
                </label>
                <motion.textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 resize-none bg-slate-50 focus:bg-white"
                  rows={3}
                  placeholder={`请简要描述${formData.type === 'knowledge' ? '知识库' : '插件'}的功能`}
                  required
                  whileFocus={{ scale: 1.02 }}
                />
              </motion.div>

              {/* Action Buttons */}
              <motion.div 
                className="flex space-x-3 pt-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <motion.button
                  type="button"
                  onClick={onClose}
                  className="flex-1 px-4 py-3 text-slate-700 bg-slate-100 rounded-xl hover:bg-slate-200 transition-all duration-300 font-medium"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  取消
                </motion.button>
                <motion.button
                  type="submit"
                  className={`flex-1 px-4 py-3 text-white rounded-xl transition-all duration-300 font-medium shadow-lg ${
                    formData.type === 'knowledge'
                      ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-green-500/25'
                      : 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-purple-500/25'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  创建资源
                </motion.button>
              </motion.div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};