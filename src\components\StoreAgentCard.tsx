import React from 'react';
import { motion } from 'framer-motion';
import { Users, Star, Crown } from 'lucide-react';
import { StoreAgent } from '../types';

interface StoreAgentCardProps {
  agent: StoreAgent;
  index: number;
}

export const StoreAgentCard: React.FC<StoreAgentCardProps> = ({ agent, index }) => {
  const formatFollowers = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  return (
    <motion.div
      className="bg-white rounded-2xl border border-slate-200/60 p-4 hover:shadow-xl transition-all duration-500 hover:border-blue-300/50 group cursor-pointer"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.5, 
        delay: index * 0.05,
        ease: "easeOut"
      }}
      whileHover={{ 
        y: -4,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
    >
      {/* Header with avatar and name */}
      <div className="flex items-start space-x-3 mb-3">
        <motion.div 
          className="w-12 h-12 rounded-2xl flex items-center justify-center text-2xl bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200/50 shadow-sm"
          whileHover={{ 
            scale: 1.1, 
            rotate: 5,
          }}
          transition={{ type: "spring", stiffness: 400, damping: 17 }}
        >
          {agent.avatar}
        </motion.div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <motion.h3 
              className="font-semibold text-slate-900 truncate group-hover:text-blue-700 transition-colors duration-300 text-base"
              initial={{ opacity: 0.8 }}
              whileHover={{ opacity: 1 }}
            >
              {agent.name}
            </motion.h3>
            {agent.isOfficial && (
              <motion.div
                className="flex items-center justify-center w-5 h-5 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full"
                whileHover={{ scale: 1.1 }}
                title="官方认证"
              >
                <Crown size={12} className="text-white" />
              </motion.div>
            )}
          </div>
          
          <motion.p 
            className="text-sm text-slate-600 line-clamp-2 leading-relaxed mb-3"
            initial={{ opacity: 0.7 }}
            whileHover={{ opacity: 1 }}
          >
            {agent.description}
          </motion.p>
        </div>
      </div>

      {/* Stats */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-1 text-slate-500">
          <Users size={14} />
          <span className="text-sm font-medium">{formatFollowers(agent.followers)}</span>
          <span className="text-sm">会话</span>
        </div>
        
        <div className="text-sm text-slate-500">
          {agent.author}
        </div>
      </div>

      {/* Tags */}
      <div className="flex flex-wrap gap-1.5">
        {agent.tags.slice(0, 3).map((tag, tagIndex) => (
          <motion.span
            key={tagIndex}
            className="px-2.5 py-1 bg-gradient-to-r from-slate-100 to-gray-100 text-slate-600 rounded-lg text-xs font-medium hover:from-blue-100 hover:to-indigo-100 hover:text-blue-700 transition-all duration-300"
            whileHover={{ scale: 1.05 }}
          >
            {tag}
          </motion.span>
        ))}
        {agent.tags.length > 3 && (
          <motion.span
            className="px-2.5 py-1 bg-gradient-to-r from-slate-100 to-gray-100 text-slate-500 rounded-lg text-xs font-medium"
            whileHover={{ scale: 1.05 }}
          >
            +{agent.tags.length - 3}
          </motion.span>
        )}
      </div>

      {/* Featured badge */}
      {agent.featured && (
        <motion.div
          className="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-2 py-1 rounded-lg text-xs font-bold shadow-lg"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 500, damping: 15 }}
        >
          <Star size={10} className="inline mr-1" />
          精选
        </motion.div>
      )}

      {/* Hover overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl pointer-events-none"
        initial={false}
      />
    </motion.div>
  );
};