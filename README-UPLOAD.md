# 🖼️ Banner图片上传功能使用说明

## 📋 功能概述

现在Banner轮播图支持将图片直接保存到项目文件系统中，图片会永久保存在 `public/uploads/banners/` 目录下。

## 🚀 启动方式

### 方式1：同时启动前端和后端（推荐）
```bash
npm run dev:full
```

### 方式2：分别启动
```bash
# 终端1：启动后端服务器
npm run server

# 终端2：启动前端开发服务器
npm run dev
```

## 🔧 服务器配置

- **后端服务器**: http://localhost:3001
- **前端服务器**: http://localhost:5173
- **图片存储目录**: `public/uploads/banners/`
- **图片访问URL**: `http://localhost:3001/uploads/banners/文件名`

## 📁 文件结构

```
项目根目录/
├── server.js                 # 后端服务器
├── public/
│   └── uploads/
│       └── banners/          # 图片存储目录
├── src/
│   └── components/
│       └── StoreBanner.tsx   # 轮播图组件
└── package.json
```

## ✨ 功能特性

### 🔄 图片管理
- ✅ 拖拽上传图片
- ✅ 点击选择图片
- ✅ 多文件同时上传
- ✅ 图片格式验证（jpg, png, gif, webp）
- ✅ 文件大小限制（5MB）
- ✅ 删除单张图片
- ✅ 清除所有图片

### 🎠 轮播功能
- ✅ 自动轮播（4秒间隔）
- ✅ 悬停暂停
- ✅ 手动切换（左右箭头）
- ✅ 指示点导航
- ✅ 平滑动画效果

### 💾 数据持久化
- ✅ 图片保存到项目文件系统
- ✅ 刷新页面图片不丢失
- ✅ 重启服务器图片保持
- ✅ 可通过URL直接访问图片

## 🛠️ API接口

### 上传图片
```
POST /api/upload-banner
Content-Type: multipart/form-data
Body: { image: File }
```

### 获取图片列表
```
GET /api/banners
Response: { success: true, banners: [...] }
```

### 删除图片
```
DELETE /api/banners/:filename
Response: { success: true, message: "图片删除成功" }
```

### 清除所有图片
```
DELETE /api/banners
Response: { success: true, message: "所有图片删除成功" }
```

## 🔒 安全特性

- 文件类型验证
- 文件大小限制
- 唯一文件名生成
- CORS跨域支持
- 错误处理和用户提示

## 📝 注意事项

1. **端口占用**: 确保3001端口未被占用
2. **文件权限**: 确保有写入 `public/uploads/` 目录的权限
3. **图片格式**: 支持 jpg, jpeg, png, gif, webp 格式
4. **文件大小**: 单个文件最大5MB
5. **存储空间**: 注意磁盘空间使用情况

## 🐛 故障排除

### 上传失败
- 检查后端服务器是否启动
- 检查文件格式和大小
- 查看浏览器控制台错误信息

### 图片不显示
- 检查图片URL是否正确
- 确认后端服务器正常运行
- 检查 `public/uploads/banners/` 目录是否存在

### 端口冲突
- 修改 `server.js` 中的 PORT 变量
- 同时修改 `StoreBanner.tsx` 中的 API_BASE_URL

## 🎯 使用步骤

1. 启动服务器：`npm run dev:full`
2. 打开浏览器：http://localhost:5173
3. 点击"商店"导航
4. 在Banner区域上传图片
5. 享受轮播效果！

现在你的图片会永久保存在项目中，不会因为刷新页面而丢失！
