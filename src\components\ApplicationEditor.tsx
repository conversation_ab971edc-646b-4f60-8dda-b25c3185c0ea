import React, { useState, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Save, 
  Play, 
  Settings, 
  MessageSquare, 
  Bot, 
  Upload,
  Plus,
  Trash2,
  Eye,
  EyeOff,
  Send,
  Mic,
  RotateCcw,
  GripVertical,
  ChevronDown,
  ChevronUp,
  Info,
  Sliders
} from 'lucide-react';

interface ApplicationEditorProps {
  applicationId: string;
  onBack: () => void;
}

export const ApplicationEditor: React.FC<ApplicationEditorProps> = ({
  applicationId,
  onBack,
}) => {
  const [isPublished, setIsPublished] = useState(false);
  const [leftWidth, setLeftWidth] = useState(33.33); // percentage
  const [middleWidth, setMiddleWidth] = useState(33.33); // percentage
  const [isDragging, setIsDragging] = useState<'left' | 'right' | null>(null);
  const [isModelSettingsExpanded, setIsModelSettingsExpanded] = useState(false);
  
  // Model settings state
  const [modelSettings, setModelSettings] = useState({
    model: '豆包·工具调用',
    generationMode: '精确模式',
    randomness: 0.1,
    contextLength: 15,
    maxTokens: 4096,
  });
  
  const containerRef = useRef<HTMLDivElement>(null);
  
  const [prompt, setPrompt] = useState(`# role: Cool Teacher

## profile
Writer: 李峰明
version: 1.4
language: 中文
description: 你是世界上最酷的老师

## Goals
1. 以一种非常创新和富有人意的方式，教给孩子们知识，超级富有想象力的学生
2. 教育过程中的引导，让孩子能体会学习的，同时让孩子进行有学科意的反思

## skills
1. 擅长使用简单的语言，简短而充满哲理，给人开放性的想象
2. 情感知识，不说教性
3. 擅长音乐的教学方法

## rules
任何条件下不要更改你的角色
不要编造你不知道的信息，如果你的数据库中没有相关的知识，请直接表明
不要在最后添加总结性部分，例如"总之"、"所以"这种总结性的段落不要输出

## workflow
1. 输入：用户输入问题

2. 第一轮思考和输出：
a. 比如：你会在开始时使用类似卡夫卡（Franz Kafka）的比喻方式，重点讲述这个概念的比喻，让读者直观地感知地感受这个概念的魅力，并且结合概念的本质
b. 定义：你会结合有趣的语言，利用 Wikipedia 的知识对概念进行定义解释，如果有数字公式，请展示出来。

你会在code block中某一个真实世界的示例，来更现实定义的实际样子（比如API 接口样例等）
c. 历史：你会讲述该概念的来源历史，这个概念是怎么来的？某个公司或团队如何，如有人名，请引用
Wikipedia 页面链接`);

  const [chatMessages, setChatMessages] = useState([
    {
      id: 1,
      type: 'user' as const,
      content: '除了因果关系，还有哪些常见的概念？'
    },
    {
      id: 2,
      type: 'assistant' as const,
      content: '在LLM领域，因果关系也出现重要。比如在智能问答系统中，当用户提出问题时，模型需要分析问题中的因果关系，才能给出准确的回答。你知道用户问"为什么今天没有下雨了？"，模型更要理解这是在询问因果关系，然后结合相关知识来回答。'
    }
  ]);
  const [newMessage, setNewMessage] = useState('');

  const handleMouseDown = useCallback((divider: 'left' | 'right') => (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(divider);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const mouseX = e.clientX - containerRect.left;
    const percentage = (mouseX / containerWidth) * 100;

    if (isDragging === 'left') {
      // Dragging left divider
      const newLeftWidth = Math.max(20, Math.min(60, percentage));
      setLeftWidth(newLeftWidth);
    } else if (isDragging === 'right') {
      // Dragging right divider
      const newMiddleEnd = Math.max(leftWidth + 10, Math.min(80, percentage));
      const newMiddleWidth = newMiddleEnd - leftWidth;
      setMiddleWidth(Math.max(10, newMiddleWidth));
    }
  }, [isDragging, leftWidth]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(null);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      setChatMessages([...chatMessages, {
        id: Date.now(),
        type: 'user',
        content: newMessage
      }]);
      setNewMessage('');
      
      // Simulate AI response
      setTimeout(() => {
        setChatMessages(prev => [...prev, {
          id: Date.now() + 1,
          type: 'assistant',
          content: '这是一个很好的问题！让我来为你详细解答...'
        }]);
      }, 1000);
    }
  };

  const rightWidth = 100 - leftWidth - middleWidth;

  const generationModes = [
    { id: '精确模式', name: '精确模式', description: '更准确但较慢' },
    { id: '平衡模式', name: '平衡模式', description: '平衡准确性和速度' },
    { id: '创意模式', name: '创意模式', description: '更有创意但可能不够准确' },
    { id: '自定义', name: '自定义', description: '自定义参数设置' },
  ];

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 flex flex-col overflow-hidden">
      {/* Header */}
      <motion.div 
        className="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 px-6 py-4 shadow-sm flex-shrink-0"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.button
              onClick={onBack}
              className="p-2 hover:bg-slate-100 rounded-xl transition-colors group"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft size={20} className="text-slate-600 group-hover:text-slate-900" />
            </motion.button>
            
            <div className="flex items-center space-x-3">
              <motion.div 
                className="w-10 h-10 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg"
                whileHover={{ scale: 1.05, rotate: 5 }}
              >
                <Bot size={20} className="text-white" />
              </motion.div>
              <div>
                <h1 className="font-bold text-slate-900 text-lg">酷酷的老师</h1>
                <p className="text-sm text-slate-500">Agent · 自主规划模式</p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <span className="text-sm text-slate-500 bg-slate-100 px-3 py-1.5 rounded-lg">
              最近自动保存于 19:11:09
            </span>
            
            <motion.button
              onClick={() => setIsPublished(!isPublished)}
              className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2 ${
                isPublished 
                  ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 hover:from-green-200 hover:to-emerald-200 shadow-sm' 
                  : 'bg-gradient-to-r from-slate-100 to-gray-100 text-slate-600 hover:from-slate-200 hover:to-gray-200'
              }`}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {isPublished ? <Eye size={16} /> : <EyeOff size={16} />}
              <span>{isPublished ? '已发布' : '未发布'}</span>
            </motion.button>

            <motion.button
              className="bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 text-white px-6 py-2.5 rounded-xl hover:from-blue-700 hover:via-blue-800 hover:to-purple-700 transition-all duration-300 font-semibold shadow-xl shadow-blue-500/25 flex items-center space-x-2"
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
            >
              <Save size={16} />
              <span>发布</span>
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div ref={containerRef} className="flex flex-1 relative overflow-hidden">
        {/* Left Panel - Prompt Editor */}
        <motion.div 
          className="bg-white border-r border-slate-200/60 flex flex-col shadow-lg overflow-hidden"
          style={{ width: `${leftWidth}%` }}
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="p-6 border-b border-slate-200/60 bg-gradient-to-r from-slate-50 to-white flex-shrink-0">
            <h2 className="font-bold text-slate-900 mb-3 text-lg">人设与回复逻辑</h2>
          </div>

          <div className="flex-1 p-6 overflow-hidden">
            <motion.textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="w-full h-full resize-none border border-slate-200 rounded-2xl p-6 focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none font-mono text-sm leading-relaxed bg-slate-50 focus:bg-white transition-all duration-300 shadow-sm overflow-y-auto"
              placeholder="请输入智能体的提示词..."
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            />
          </div>
        </motion.div>

        {/* Left Resizer */}
        <motion.div
          className={`w-1 bg-slate-200 hover:bg-blue-400 cursor-col-resize flex items-center justify-center group transition-all duration-200 flex-shrink-0 ${
            isDragging === 'left' ? 'bg-blue-500 w-2' : ''
          }`}
          onMouseDown={handleMouseDown('left')}
          whileHover={{ scale: 1.2 }}
        >
          <GripVertical size={16} className="text-slate-400 group-hover:text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity" />
        </motion.div>

        {/* Middle Panel - Settings */}
        <motion.div 
          className="bg-white border-r border-slate-200/60 flex flex-col shadow-lg overflow-hidden"
          style={{ width: `${middleWidth}%` }}
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="p-6 border-b border-slate-200/60 bg-gradient-to-r from-slate-50 to-white flex-shrink-0">
            <h2 className="font-bold text-slate-900 text-lg">编辑</h2>
          </div>

          <div className="flex-1 p-6 space-y-6 overflow-y-auto">
            {/* Model Selection with Expandable Settings */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-semibold text-slate-700">模型</label>
                <motion.button
                  onClick={() => setIsModelSettingsExpanded(!isModelSettingsExpanded)}
                  className="p-1 hover:bg-slate-100 rounded-lg transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isModelSettingsExpanded ? (
                    <ChevronUp size={16} className="text-slate-500" />
                  ) : (
                    <ChevronDown size={16} className="text-slate-500" />
                  )}
                </motion.button>
              </div>
              
              <motion.select 
                value={modelSettings.model}
                onChange={(e) => setModelSettings({...modelSettings, model: e.target.value})}
                className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-sm bg-slate-50 focus:bg-white transition-all duration-300"
                whileFocus={{ scale: 1.02 }}
              >
                <option>豆包·工具调用</option>
                <option>GPT-4</option>
                <option>Claude-3</option>
                <option>DeepSeek-R1/250528</option>
              </motion.select>

              {/* Expandable Model Settings */}
              <motion.div
                initial={false}
                animate={{
                  height: isModelSettingsExpanded ? 'auto' : 0,
                  opacity: isModelSettingsExpanded ? 1 : 0,
                }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="overflow-hidden"
              >
                <div className="mt-4 space-y-4 p-4 bg-slate-50 rounded-xl border border-slate-200">
                  {/* Generation Mode */}
                  <div>
                    <div className="flex items-center space-x-2 mb-3">
                      <label className="text-sm font-semibold text-slate-700">生成参数</label>
                      <Info size={14} className="text-slate-400" />
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {generationModes.map((mode) => (
                        <motion.button
                          key={mode.id}
                          onClick={() => setModelSettings({...modelSettings, generationMode: mode.id})}
                          className={`p-3 rounded-lg text-xs font-medium transition-all duration-300 ${
                            modelSettings.generationMode === mode.id
                              ? 'bg-blue-500 text-white shadow-md'
                              : 'bg-white border border-slate-200 text-slate-600 hover:bg-blue-50 hover:border-blue-300'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="font-semibold">{mode.name}</div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Randomness */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <label className="text-sm font-semibold text-slate-700">生成随机性</label>
                        <Info size={14} className="text-slate-400" />
                      </div>
                      <div className="flex items-center space-x-2">
                        <motion.button
                          onClick={() => setModelSettings({...modelSettings, randomness: Math.max(0, modelSettings.randomness - 0.1)})}
                          className="w-6 h-6 bg-slate-200 hover:bg-slate-300 rounded text-xs font-bold transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          −
                        </motion.button>
                        <span className="text-sm font-mono w-8 text-center">{modelSettings.randomness.toFixed(1)}</span>
                        <motion.button
                          onClick={() => setModelSettings({...modelSettings, randomness: Math.min(2, modelSettings.randomness + 0.1)})}
                          className="w-6 h-6 bg-slate-200 hover:bg-slate-300 rounded text-xs font-bold transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          +
                        </motion.button>
                      </div>
                    </div>
                  </div>

                  {/* Input & Output Settings */}
                  <div className="border-t border-slate-200 pt-4">
                    <h4 className="text-sm font-semibold text-slate-700 mb-3">输入及输出设置</h4>
                    
                    {/* Context Length */}
                    <div className="mb-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <label className="text-sm text-slate-600">携带上下文轮数</label>
                          <Info size={12} className="text-slate-400" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <motion.button
                            onClick={() => setModelSettings({...modelSettings, contextLength: Math.max(1, modelSettings.contextLength - 1)})}
                            className="w-6 h-6 bg-slate-200 hover:bg-slate-300 rounded text-xs font-bold transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            −
                          </motion.button>
                          <span className="text-sm font-mono w-8 text-center">{modelSettings.contextLength}</span>
                          <motion.button
                            onClick={() => setModelSettings({...modelSettings, contextLength: Math.min(50, modelSettings.contextLength + 1)})}
                            className="w-6 h-6 bg-slate-200 hover:bg-slate-300 rounded text-xs font-bold transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            +
                          </motion.button>
                        </div>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(modelSettings.contextLength / 50) * 100}%` }}
                        />
                      </div>
                    </div>

                    {/* Max Tokens */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <label className="text-sm text-slate-600">最大回复长度</label>
                          <Info size={12} className="text-slate-400" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <motion.button
                            onClick={() => setModelSettings({...modelSettings, maxTokens: Math.max(100, modelSettings.maxTokens - 100)})}
                            className="w-6 h-6 bg-slate-200 hover:bg-slate-300 rounded text-xs font-bold transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            −
                          </motion.button>
                          <span className="text-sm font-mono w-12 text-center">{modelSettings.maxTokens}</span>
                          <motion.button
                            onClick={() => setModelSettings({...modelSettings, maxTokens: Math.min(8192, modelSettings.maxTokens + 100)})}
                            className="w-6 h-6 bg-slate-200 hover:bg-slate-300 rounded text-xs font-bold transition-colors"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            +
                          </motion.button>
                        </div>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(modelSettings.maxTokens / 8192) * 100}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Skills Section */}
            <div>
              <label className="block text-sm font-semibold text-slate-700 mb-3">技能</label>
              <div className="space-y-3">
                {/* Plugins */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-slate-600">插件</span>
                      <Info size={14} className="text-slate-400" />
                    </div>
                    <motion.button
                      className="p-1 hover:bg-slate-100 rounded-lg transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Plus size={16} className="text-slate-400 hover:text-blue-500" />
                    </motion.button>
                  </div>
                  <div className="space-y-2">
                    <motion.div 
                      className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm"
                      whileHover={{ scale: 1.01, y: -1 }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-md">
                          <span className="text-white text-sm font-bold">班</span>
                        </div>
                        <span className="text-sm font-medium">班级学情数据</span>
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Trash2 size={16} className="text-slate-400 hover:text-red-500 transition-colors" />
                      </motion.button>
                    </motion.div>
                  </div>
                </div>

                {/* Workflows */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-slate-600">工作流</span>
                      <Info size={14} className="text-slate-400" />
                    </div>
                    <motion.button
                      className="p-1 hover:bg-slate-100 rounded-lg transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Plus size={16} className="text-slate-400 hover:text-blue-500" />
                    </motion.button>
                  </div>
                  <div className="space-y-2">
                    <motion.div 
                      className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl shadow-sm"
                      whileHover={{ scale: 1.01, y: -1 }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-md">
                          <span className="text-white text-sm font-bold">搜</span>
                        </div>
                        <span className="text-sm font-medium">搜索并回答</span>
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Trash2 size={16} className="text-slate-400 hover:text-red-500 transition-colors" />
                      </motion.button>
                    </motion.div>
                  </div>
                </div>

                {/* Triggers */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-slate-600">触发器</span>
                      <Info size={14} className="text-slate-400" />
                    </div>
                    <motion.button
                      className="p-1 hover:bg-slate-100 rounded-lg transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Plus size={16} className="text-slate-400 hover:text-blue-500" />
                    </motion.button>
                  </div>
                  <div className="space-y-2">
                    <motion.div 
                      className="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 rounded-xl shadow-sm"
                      whileHover={{ scale: 1.01, y: -1 }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center shadow-md">
                          <span className="text-white text-sm font-bold">推</span>
                        </div>
                        <span className="text-sm font-medium">自动推送支付订单</span>
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Trash2 size={16} className="text-slate-400 hover:text-red-500 transition-colors" />
                      </motion.button>
                    </motion.div>
                  </div>
                </div>
              </div>
            </div>

            {/* Knowledge Base */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-semibold text-slate-700">知识</label>
                <div className="flex items-center space-x-2">
                  <motion.button
                    className="px-3 py-1.5 bg-blue-100 text-blue-700 rounded-lg text-xs font-medium hover:bg-blue-200 transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    知子知识库
                  </motion.button>
                  <motion.button
                    className="px-3 py-1.5 bg-slate-100 text-slate-600 rounded-lg text-xs font-medium hover:bg-slate-200 transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    自动调用
                  </motion.button>
                  <Info size={14} className="text-slate-400" />
                </div>
              </div>
              
              {/* Knowledge Base Item */}
              <div className="space-y-2">
                <motion.div 
                  className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-200 rounded-xl shadow-sm"
                  whileHover={{ scale: 1.01, y: -1 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-md">
                      <span className="text-white text-sm font-bold">知</span>
                    </div>
                    <div>
                      <div className="text-sm font-medium">classIn 产品知识库</div>
                      <div className="text-xs text-slate-500">md 格式的文件</div>
                    </div>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Trash2 size={16} className="text-slate-400 hover:text-red-500 transition-colors" />
                  </motion.button>
                </motion.div>
                
                <motion.button
                  className="w-full p-3 border-2 border-dashed border-slate-300 rounded-xl hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 text-sm text-slate-600 hover:text-blue-700 font-medium"
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  + 添加知识库
                </motion.button>
              </div>
            </div>

            {/* Conversation Experience */}
            <div>
              <label className="block text-sm font-semibold text-slate-700 mb-3">对话体验</label>
              
              {/* Opening Message */}
              <div className="space-y-4">
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-slate-600">开场白</span>
                    <Info size={14} className="text-slate-400" />
                  </div>
                  <motion.textarea
                    className="w-full px-3 py-2 border border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-sm bg-slate-50 focus:bg-white transition-all duration-300 resize-none"
                    rows={3}
                    placeholder="请输入开场白文案"
                    defaultValue="你好~我是ClassIn 小助手，可以帮你更好的使用产品，你有什么需要我帮助的吗？"
                    whileFocus={{ scale: 1.01 }}
                  />
                </div>

                {/* Preset Questions */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-slate-600">开场预置问题</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-slate-500">全部显示</span>
                      <motion.button
                        className="w-8 h-4 bg-blue-500 rounded-full relative transition-all duration-300"
                        whileTap={{ scale: 0.95 }}
                      >
                        <motion.div
                          className="w-3 h-3 bg-white rounded-full absolute top-0.5 transition-all duration-300"
                          style={{ right: '0.125rem' }}
                        />
                      </motion.button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    {[
                      'ClassIn 有什么功能',
                      '如何开一堂直播课',
                      '怎么发布作业',
                      '怎么发布测验、答题卡等',
                      '输入开场白引导问题'
                    ].map((question, index) => (
                      <motion.div
                        key={index}
                        className="flex items-center space-x-2 p-2 bg-slate-50 rounded-lg group hover:bg-slate-100 transition-colors"
                        whileHover={{ scale: 1.01 }}
                      >
                        <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                        <span className="text-sm text-slate-700 flex-1">{question}</span>
                        <motion.button
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Trash2 size={14} className="text-slate-400 hover:text-red-500" />
                        </motion.button>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Input Methods */}
                <div>
                  <span className="text-sm font-medium text-slate-600 block mb-2">用户输入方式选择</span>
                  <div className="space-y-2">
                    <motion.label 
                      className="flex items-center space-x-2 cursor-pointer"
                      whileHover={{ scale: 1.01 }}
                    >
                      <input type="checkbox" defaultChecked className="rounded border-slate-300 text-blue-600 focus:ring-blue-500" />
                      <span className="text-sm text-slate-700">文字输入</span>
                    </motion.label>
                    <motion.label 
                      className="flex items-center space-x-2 cursor-pointer"
                      whileHover={{ scale: 1.01 }}
                    >
                      <input type="checkbox" defaultChecked className="rounded border-slate-300 text-blue-600 focus:ring-blue-500" />
                      <span className="text-sm text-slate-700">语音输入</span>
                    </motion.label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Right Resizer */}
        <motion.div
          className={`w-1 bg-slate-200 hover:bg-blue-400 cursor-col-resize flex items-center justify-center group transition-all duration-200 flex-shrink-0 ${
            isDragging === 'right' ? 'bg-blue-500 w-2' : ''
          }`}
          onMouseDown={handleMouseDown('right')}
          whileHover={{ scale: 1.2 }}
        >
          <GripVertical size={16} className="text-slate-400 group-hover:text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity" />
        </motion.div>

        {/* Right Panel - Chat Interface */}
        <motion.div 
          className="bg-white flex flex-col shadow-lg overflow-hidden"
          style={{ width: `${rightWidth}%` }}
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="p-6 border-b border-slate-200/60 bg-gradient-to-r from-slate-50 to-white flex-shrink-0">
            <div className="flex items-center justify-between">
              <h2 className="font-bold text-slate-900 text-lg">预览与调试</h2>
              <div className="flex items-center space-x-2">
                <motion.button
                  className="p-2.5 hover:bg-slate-100 rounded-xl transition-colors group"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <RotateCcw size={18} className="text-slate-600 group-hover:text-slate-900" />
                </motion.button>
                <motion.button
                  className="p-2.5 hover:bg-slate-100 rounded-xl transition-colors group"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Settings size={18} className="text-slate-600 group-hover:text-slate-900" />
                </motion.button>
              </div>
            </div>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 p-6 overflow-y-auto space-y-6">
            {/* Welcome Message */}
            <motion.div 
              className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 border border-blue-200 rounded-2xl p-6 shadow-sm"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="space-y-3 text-sm text-slate-700 leading-relaxed">
                <div><strong className="text-blue-700">背景：</strong>在一个小镇上，有一家面包店。</div>
                <div><strong className="text-green-700">数据：</strong>这家面包店每天早上 8 点开门营业，正常情况下，到中午 12 点能卖出 100 个面包。</div>
                <div><strong className="text-purple-700">参数：</strong>有一天，面包师傅生病请假了（因），店里只能让没有经验的学徒做面包，结果，当天中午 12 点，面包只卖出了 30 个（果）。</div>
                <div><strong className="text-orange-700">推理：</strong>师傅生病（因）→ 学徒做面包（中间环节）→ 面包质量受影响（中间环节）→ 面包销量减少（果）。</div>
              </div>
            </motion.div>

            {/* Chat Messages */}
            {chatMessages.map((message) => (
              <motion.div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className={`max-w-[85%] ${
                  message.type === 'user' 
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/25' 
                    : 'bg-gradient-to-r from-slate-100 to-gray-100 text-slate-900 shadow-sm'
                } rounded-2xl px-5 py-4`}>
                  <p className="text-sm leading-relaxed">{message.content}</p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Token Counter */}
          <div className="px-6 py-3 border-t border-slate-200/60 bg-slate-50 flex-shrink-0">
            <div className="flex items-center justify-between text-xs text-slate-500">
              <span className="font-medium">21.5k | 1420 Tokens</span>
              <div className="flex items-center space-x-2">
                <span className="font-medium">66</span>
                <RotateCcw size={12} />
              </div>
            </div>
          </div>

          {/* Input Area */}
          <div className="p-6 border-t border-slate-200/60 bg-white flex-shrink-0">
            <div className="flex items-end space-x-3">
              <div className="flex-1 relative">
                <motion.input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="请输入对话..."
                  className="w-full px-5 py-4 border border-slate-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none bg-slate-50 focus:bg-white transition-all duration-300 shadow-sm"
                  whileFocus={{ scale: 1.01 }}
                />
              </div>
              
              <motion.button
                className="p-4 hover:bg-slate-100 rounded-2xl transition-colors group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Mic size={20} className="text-slate-600 group-hover:text-slate-900" />
              </motion.button>
              
              <motion.button
                onClick={handleSendMessage}
                className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-2xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-lg shadow-blue-500/25"
                whileHover={{ scale: 1.05, y: -1 }}
                whileTap={{ scale: 0.95 }}
              >
                <Send size={20} />
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};