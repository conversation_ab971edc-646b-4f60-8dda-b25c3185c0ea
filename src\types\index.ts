export interface AIApplication {
  id: string;
  name: string;
  description: string;
  avatar: string;
  isPublished: boolean;
  lastEdited: Date;
  category?: string;
}

export interface NavigationItem {
  id: string;
  name: string;
  icon: string;
  secondaryItems?: SecondaryNavigationItem[];
}

export interface SecondaryNavigationItem {
  id: string;
  name: string;
  count?: number;
}

export interface Resource {
  id: string;
  name: string;
  description: string;
  type: 'knowledge' | 'plugin';
  category: string;
  size?: string;
  lastModified: Date;
  status: 'active' | 'inactive' | 'processing';
  icon?: string;
  tags?: string[];
  fileCount?: number;
}

export interface KnowledgeBase extends Resource {
  type: 'knowledge';
  documentCount: number;
  totalSize: string;
  sources: string[];
}

export interface Plugin extends Resource {
  type: 'plugin';
  version: string;
  author: string;
  downloads: number;
  rating: number;
}

// 新增商店智能体类型
export interface StoreAgent {
  id: string;
  name: string;
  description: string;
  avatar: string;
  category: string;
  followers: number;
  author: string;
  tags: string[];
  rating?: number;
  isOfficial?: boolean;
  featured?: boolean;
}