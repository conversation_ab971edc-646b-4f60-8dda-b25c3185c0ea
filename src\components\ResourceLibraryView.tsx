import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  Filter, 
  Plus, 
  Database, 
  Puzzle, 
  FileText, 
  Calendar,
  MoreHorizontal,
  Download,
  Star,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';
import { Resource, KnowledgeBase, Plugin } from '../types';
import { CreateResourceModal } from './CreateResourceModal';

interface ResourceLibraryViewProps {
  resources: Resource[];
  onCreateResource: (data: Partial<Resource>) => string;
  onDeleteResource: (id: string) => void;
  onUpdateResource: (id: string, updates: Partial<Resource>) => void;
}

export const ResourceLibraryView: React.FC<ResourceLibraryViewProps> = ({
  resources,
  onCreateResource,
  onDeleteResource,
  onUpdateResource,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'knowledge' | 'plugin'>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const categories = useMemo(() => {
    const cats = Array.from(new Set(resources.map(r => r.category)));
    return ['all', ...cats];
  }, [resources]);

  const filteredResources = useMemo(() => {
    return resources.filter((resource) => {
      const matchesSearch = resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           resource.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesType = typeFilter === 'all' || resource.type === typeFilter;
      const matchesCategory = categoryFilter === 'all' || resource.category === categoryFilter;
      
      return matchesSearch && matchesType && matchesCategory;
    }).sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime());
  }, [resources, searchTerm, typeFilter, categoryFilter]);

  const getStatusIcon = (status: Resource['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'inactive':
        return <AlertCircle size={16} className="text-yellow-500" />;
      case 'processing':
        return <Loader size={16} className="text-blue-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: Resource['status']) => {
    switch (status) {
      case 'active':
        return '活跃';
      case 'inactive':
        return '未激活';
      case 'processing':
        return '处理中';
      default:
        return '未知';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const ResourceCard: React.FC<{ resource: Resource; index: number }> = ({ resource, index }) => {
    const isKnowledge = resource.type === 'knowledge';
    const knowledgeResource = resource as KnowledgeBase;
    const pluginResource = resource as Plugin;

    return (
      <motion.div
        className="bg-white rounded-2xl border border-slate-200/60 p-6 hover:shadow-xl transition-all duration-500 hover:border-blue-300/50 group"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ 
          duration: 0.5, 
          delay: index * 0.1,
          ease: "easeOut"
        }}
        whileHover={{ 
          y: -4,
          transition: { duration: 0.3, ease: "easeOut" }
        }}
      >
        <div className="flex items-start space-x-4">
          <motion.div 
            className={`w-12 h-12 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg ${
              isKnowledge 
                ? 'bg-gradient-to-br from-green-500 via-green-600 to-emerald-600' 
                : 'bg-gradient-to-br from-purple-500 via-purple-600 to-indigo-600'
            }`}
            whileHover={{ 
              scale: 1.1, 
              rotate: 5,
            }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            {isKnowledge ? <Database size={24} /> : <Puzzle size={24} />}
          </motion.div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <motion.h3 
                    className="font-semibold text-slate-900 truncate group-hover:text-blue-700 transition-colors duration-300"
                    initial={{ opacity: 0.8 }}
                    whileHover={{ opacity: 1 }}
                  >
                    {resource.name}
                  </motion.h3>
                  {getStatusIcon(resource.status)}
                </div>
                <motion.p 
                  className="text-sm text-slate-600 mb-3 line-clamp-2 leading-relaxed"
                  initial={{ opacity: 0.7 }}
                  whileHover={{ opacity: 1 }}
                >
                  {resource.description}
                </motion.p>
              </div>
              
              <motion.button
                className="opacity-0 group-hover:opacity-100 p-2 hover:bg-slate-100 rounded-lg transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <MoreHorizontal size={16} className="text-slate-400" />
              </motion.button>
            </div>
            
            {/* Resource-specific info */}
            <div className="space-y-2 mb-4">
              {isKnowledge ? (
                <div className="flex items-center space-x-4 text-xs text-slate-500">
                  <div className="flex items-center space-x-1">
                    <FileText size={12} />
                    <span>{knowledgeResource.documentCount} 文档</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Database size={12} />
                    <span>{knowledgeResource.totalSize}</span>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-4 text-xs text-slate-500">
                  <div className="flex items-center space-x-1">
                    <Download size={12} />
                    <span>{pluginResource.downloads}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star size={12} />
                    <span>{pluginResource.rating}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users size={12} />
                    <span>{pluginResource.author}</span>
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex items-center justify-between">
              <motion.span
                className={`
                  px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-300 flex items-center space-x-1.5
                  ${resource.type === 'knowledge'
                    ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-700'
                    : 'bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-700'
                  }
                `}
                whileHover={{ scale: 1.05 }}
              >
                <span>{resource.category}</span>
              </motion.span>
              
              <div className="flex items-center text-xs text-slate-500">
                <Calendar size={12} className="mr-1.5" />
                <span>{formatDate(resource.lastModified)}</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="flex-1 p-8 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div 
          className="flex items-center justify-between mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div>
            <motion.h1 
              className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-blue-800 to-purple-800 bg-clip-text text-transparent"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              资源库
            </motion.h1>
            <motion.p 
              className="text-slate-600 mt-2 text-lg"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              管理你的知识库和插件资源
            </motion.p>
          </div>
          
          <motion.button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-to-r from-blue-600 via-blue-700 to-purple-600 text-white px-6 py-3 rounded-2xl hover:from-blue-700 hover:via-blue-800 hover:to-purple-700 transition-all duration-300 flex items-center space-x-3 shadow-xl shadow-blue-500/25 group"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <Plus size={20} />
            <span className="font-semibold">添加资源</span>
          </motion.button>
        </motion.div>

        {/* Filters */}
        <motion.div 
          className="flex items-center space-x-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400" size={20} />
            <motion.input
              type="text"
              placeholder="搜索资源名称或描述"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-slate-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm"
              whileFocus={{ scale: 1.02 }}
            />
          </div>
          
          <div className="flex items-center space-x-3">
            <Filter className="text-slate-400" size={20} />
            <motion.select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value as 'all' | 'knowledge' | 'plugin')}
              className="px-4 py-3 border border-slate-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm"
              whileFocus={{ scale: 1.02 }}
            >
              <option value="all">所有类型</option>
              <option value="knowledge">知识库</option>
              <option value="plugin">插件</option>
            </motion.select>
            
            <motion.select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-4 py-3 border border-slate-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-sm"
              whileFocus={{ scale: 1.02 }}
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? '全部' : category}
                </option>
              ))}
            </motion.select>
          </div>
        </motion.div>

        {/* Resources Grid */}
        <motion.div 
          className="grid grid-cols-1 lg:grid-cols-2 gap-6"
          initial="hidden"
          animate="visible"
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {filteredResources.map((resource, index) => (
            <ResourceCard key={resource.id} resource={resource} index={index} />
          ))}
        </motion.div>

        {/* Empty State */}
        {filteredResources.length === 0 && (
          <motion.div 
            className="text-center py-16"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div 
              className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg"
              animate={{ 
                rotate: [0, 5, -5, 0],
                scale: [1, 1.05, 1]
              }}
              transition={{ 
                duration: 4, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <Database className="text-blue-500" size={32} />
            </motion.div>
            <h3 className="text-2xl font-bold text-slate-900 mb-3">没有找到资源</h3>
            <p className="text-slate-600 mb-6 text-lg">尝试调整搜索条件或添加新的资源</p>
            <motion.button
              onClick={() => setIsCreateModalOpen(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-2xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 font-semibold shadow-lg shadow-blue-500/25"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              添加第一个资源
            </motion.button>
          </motion.div>
        )}
      </div>

      <CreateResourceModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={onCreateResource}
      />
    </div>
  );
};