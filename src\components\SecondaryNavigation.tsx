import React from 'react';
import { motion } from 'framer-motion';

interface SecondaryNavigationProps {
  activeSection: string;
  activeSubSection: string;
  onSubSectionChange: (subSection: string) => void;
}

const secondaryNavigationMap = {
  studio: [
    { id: 'ai-apps', name: 'AI 应用', count: 12 },
    { id: 'resources', name: '资源库', count: 8 },
    { id: 'templates', name: '模板', count: 5 },
    { id: 'evaluation', name: '评测', count: 3 },
  ],
  templates: [
    { id: 'official', name: '官方模板' },
    { id: 'community', name: '社区模板' },
    { id: 'my-templates', name: '我的模板' },
  ],
  wallet: [
    { id: 'balance', name: '余额' },
    { id: 'transactions', name: '交易记录' },
    { id: 'billing', name: '账单' },
  ],
  my: [
    { id: 'profile', name: '个人资料' },
    { id: 'settings', name: '设置' },
    { id: 'usage', name: '使用统计' },
  ],
};

export const SecondaryNavigation: React.FC<SecondaryNavigationProps> = ({
  activeSection,
  activeSubSection,
  onSubSectionChange,
}) => {
  const items = secondaryNavigationMap[activeSection as keyof typeof secondaryNavigationMap] || [];

  return (
    <motion.div 
      className="w-64 bg-gradient-to-b from-slate-50 to-white border-r border-slate-200/60 p-6 shadow-lg"
      initial={{ x: -50, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <motion.div 
        className="space-y-2"
        initial="hidden"
        animate="visible"
        variants={{
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: {
              staggerChildren: 0.1
            }
          }
        }}
      >
        {items.map((item, index) => (
          <motion.button
            key={item.id}
            onClick={() => onSubSectionChange(item.id)}
            className={`
              relative w-full text-left px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 group overflow-hidden
              ${activeSubSection === item.id
                ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/25'
                : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
              }
            `}
            variants={{
              hidden: { opacity: 0, x: -20 },
              visible: { opacity: 1, x: 0 }
            }}
            whileHover={{ scale: 1.02, x: 4 }}
            whileTap={{ scale: 0.98 }}
          >
            {/* Background gradient for active state */}
            {activeSubSection === item.id && (
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-600"
                layoutId="activeSubTab"
                transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
              />
            )}
            
            {/* Hover effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              initial={false}
            />
            
            <div className="relative z-10 flex items-center justify-between">
              <span className="font-medium">{item.name}</span>
              {item.count && (
                <motion.span 
                  className={`
                    text-xs px-2.5 py-1 rounded-full font-semibold
                    ${activeSubSection === item.id
                      ? 'bg-white/20 text-white'
                      : 'bg-slate-200 text-slate-600 group-hover:bg-blue-100 group-hover:text-blue-700'
                    }
                  `}
                  whileHover={{ scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  {item.count}
                </motion.span>
              )}
            </div>
          </motion.button>
        ))}
      </motion.div>
    </motion.div>
  );
};