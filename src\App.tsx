import React, { useState } from 'react';
import { Sidebar } from './components/Sidebar';
import { SecondaryNavigation } from './components/SecondaryNavigation';
import { ContentArea } from './components/ContentArea';
import { ApplicationEditor } from './components/ApplicationEditor';
import { useApplications } from './hooks/useApplications';
import { useResources } from './hooks/useResources';

function App() {
  const [activeSection, setActiveSection] = useState('studio');
  const [activeSubSection, setActiveSubSection] = useState('ai-apps');
  const [editingApplicationId, setEditingApplicationId] = useState<string | undefined>();
  const { applications, createApplication } = useApplications();
  const { resources, createResource, deleteResource, updateResource } = useResources();

  const handleSectionChange = (section: string) => {
    setActiveSection(section);
    setEditingApplicationId(undefined); // Clear editing state when changing sections
    // Reset to first sub-section when changing main section
    const defaultSubSections = {
      studio: 'ai-apps',
      store: 'featured',
      templates: 'official',
      wallet: 'balance',
      my: 'profile',
    };
    setActiveSubSection(defaultSubSections[section as keyof typeof defaultSubSections] || 'ai-apps');
  };

  const handleSubSectionChange = (subSection: string) => {
    setActiveSubSection(subSection);
    setEditingApplicationId(undefined); // Clear editing state when changing sub-sections
  };

  const handleEditApplication = (applicationId: string) => {
    setEditingApplicationId(applicationId);
  };

  const handleBackToApplications = () => {
    setEditingApplicationId(undefined);
  };

  // If editing an application, show only the editor (full screen)
  if (editingApplicationId) {
    return (
      <ApplicationEditor
        applicationId={editingApplicationId}
        onBack={handleBackToApplications}
      />
    );
  }

  // Otherwise show the normal layout with sidebar and navigation
  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
      />
      
      <SecondaryNavigation
        activeSection={activeSection}
        activeSubSection={activeSubSection}
        onSubSectionChange={handleSubSectionChange}
      />
      
      <ContentArea
        activeSection={activeSection}
        activeSubSection={activeSubSection}
        applications={applications}
        resources={resources}
        onCreateApplication={createApplication}
        onEditApplication={handleEditApplication}
        onCreateResource={createResource}
        onDeleteResource={deleteResource}
        onUpdateResource={updateResource}
      />
    </div>
  );
}

export default App;