import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  Filter, 
  Calendar,
  Eye,
  ChevronLeft,
  ChevronRight,
  Download,
  RefreshCw,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Minus
} from 'lucide-react';

// 交易记录类型定义
interface Transaction {
  id: string;
  orderNumber: string;
  status: 'pending' | 'completed' | 'refunding' | 'refunded' | 'closed';
  transactionTime: string;
  transactionType: 'purchase' | 'usage';
  agentName: string;
  amount: number;
  buyerInfo: string;
}

// 状态配置
const statusConfig = {
  pending: { label: '待支付', color: 'bg-orange-100 text-orange-700 border-orange-200', icon: Clock },
  completed: { label: '已完成', color: 'bg-green-100 text-green-700 border-green-200', icon: CheckCircle },
  refunding: { label: '退款中', color: 'bg-yellow-100 text-yellow-700 border-yellow-200', icon: AlertCircle },
  refunded: { label: '已退款', color: 'bg-gray-100 text-gray-700 border-gray-200', icon: Minus },
  closed: { label: '已关闭', color: 'bg-red-100 text-red-700 border-red-200', icon: XCircle },
};

// 模拟数据
const mockTransactions: Transaction[] = [
  {
    id: '1',
    orderNumber: 'TXN202501070001',
    status: 'completed',
    transactionTime: '2025-01-07 14:30:25',
    transactionType: 'purchase',
    agentName: '智能写作助手',
    amount: 299.00,
    buyerInfo: '张***'
  },
  {
    id: '2',
    orderNumber: 'TXN202501070002',
    status: 'completed',
    transactionTime: '2025-01-07 13:15:42',
    transactionType: 'usage',
    agentName: '数据分析专家',
    amount: 89.50,
    buyerInfo: '138****1234'
  },
  {
    id: '3',
    orderNumber: 'TXN202501070003',
    status: 'pending',
    transactionTime: '2025-01-07 12:45:18',
    transactionType: 'purchase',
    agentName: '代码审查助手',
    amount: 199.00,
    buyerInfo: '李***'
  },
  {
    id: '4',
    orderNumber: 'TXN202501060004',
    status: 'refunding',
    transactionTime: '2025-01-06 16:22:33',
    transactionType: 'usage',
    agentName: '翻译专家',
    amount: 45.80,
    buyerInfo: '王***'
  },
  {
    id: '5',
    orderNumber: 'TXN202501060005',
    status: 'completed',
    transactionTime: '2025-01-06 11:08:15',
    transactionType: 'purchase',
    agentName: '智能客服',
    amount: 399.00,
    buyerInfo: '186****5678'
  },
  {
    id: '6',
    orderNumber: 'TXN202501050006',
    status: 'refunded',
    transactionTime: '2025-01-05 09:30:45',
    transactionType: 'usage',
    agentName: '图像识别助手',
    amount: 128.00,
    buyerInfo: '陈***'
  },
  {
    id: '7',
    orderNumber: 'TXN202501050007',
    status: 'closed',
    transactionTime: '2025-01-05 08:15:22',
    transactionType: 'purchase',
    agentName: '语音合成专家',
    amount: 259.00,
    buyerInfo: '刘***'
  },
  {
    id: '8',
    orderNumber: 'TXN202501040008',
    status: 'completed',
    transactionTime: '2025-01-04 15:42:18',
    transactionType: 'usage',
    agentName: '智能写作助手',
    amount: 67.30,
    buyerInfo: '159****9876'
  }
];

export const WalletTransactionsView: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // 格式化货币
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // 过滤和搜索逻辑
  const filteredTransactions = useMemo(() => {
    return mockTransactions.filter(transaction => {
      const matchesSearch = 
        transaction.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.agentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        transaction.buyerInfo.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;
      const matchesType = typeFilter === 'all' || transaction.transactionType === typeFilter;
      
      return matchesSearch && matchesStatus && matchesType;
    });
  }, [searchTerm, statusFilter, typeFilter]);

  // 分页逻辑
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTransactions = filteredTransactions.slice(startIndex, startIndex + itemsPerPage);

  // 状态标签组件
  const StatusBadge: React.FC<{ status: Transaction['status'] }> = ({ status }) => {
    const config = statusConfig[status];
    const IconComponent = config.icon;
    
    return (
      <span className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium border ${config.color}`}>
        <IconComponent size={12} />
        <span>{config.label}</span>
      </span>
    );
  };

  return (
    <motion.div 
      className="flex-1 p-8 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 min-h-screen"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.6 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                交易记录
              </h1>
              <p className="text-slate-600 mt-2">查看您的智能体销售收益记录</p>
            </div>
            
            <div className="flex items-center space-x-3">
              <motion.button
                className="flex items-center space-x-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 hover:bg-white transition-all duration-300 shadow-sm"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Download size={18} />
                <span className="text-sm font-medium text-slate-700">导出</span>
              </motion.button>
              
              <motion.button
                className="flex items-center space-x-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-300 shadow-sm"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <RefreshCw size={18} />
                <span className="text-sm font-medium">刷新</span>
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Filters */}
        <motion.div
          className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-6 mb-8 shadow-lg"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <input
                type="text"
                placeholder="搜索订单号、智能体或买家"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-slate-50 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <Filter size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-slate-50 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 appearance-none"
              >
                <option value="all">全部状态</option>
                <option value="pending">待支付</option>
                <option value="completed">已完成</option>
                <option value="refunding">退款中</option>
                <option value="refunded">已退款</option>
                <option value="closed">已关闭</option>
              </select>
            </div>

            {/* Type Filter */}
            <div className="relative">
              <Calendar size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-slate-50 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 appearance-none"
              >
                <option value="all">全部类型</option>
                <option value="purchase">智能体购买</option>
                <option value="usage">智能体使用</option>
              </select>
            </div>

            {/* Results Count */}
            <div className="flex items-center justify-center bg-slate-50 rounded-xl px-4 py-3">
              <span className="text-slate-600 text-sm">
                共 <span className="font-semibold text-slate-900">{filteredTransactions.length}</span> 条记录
              </span>
            </div>
          </div>
        </motion.div>

        {/* Transactions Table */}
        <motion.div
          className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 shadow-xl overflow-hidden"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">订单号</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">交易时间</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">类型</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">智能体</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">金额</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">买家</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-100">
                {paginatedTransactions.map((transaction, index) => (
                  <motion.tr
                    key={transaction.id}
                    className="hover:bg-slate-50/50 transition-colors duration-200"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 + index * 0.05, duration: 0.4 }}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-slate-900">{transaction.orderNumber}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge status={transaction.status} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-600">{transaction.transactionTime}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        transaction.transactionType === 'purchase'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-purple-100 text-purple-800'
                      }`}>
                        {transaction.transactionType === 'purchase' ? '智能体购买' : '智能体使用'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-slate-900">{transaction.agentName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-semibold text-emerald-600">{formatCurrency(transaction.amount)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-600">{transaction.buyerInfo}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <motion.button
                        className="inline-flex items-center space-x-1 px-3 py-1.5 bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-lg transition-colors duration-200"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Eye size={14} />
                        <span className="text-xs font-medium">详情</span>
                      </motion.button>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {paginatedTransactions.length === 0 && (
            <motion.div
              className="text-center py-12"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.6 }}
            >
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search size={24} className="text-slate-400" />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">暂无交易记录</h3>
              <p className="text-slate-500">没有找到符合条件的交易记录</p>
            </motion.div>
          )}
        </motion.div>

        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div
            className="flex items-center justify-between mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <div className="text-sm text-slate-600">
              显示第 {startIndex + 1} - {Math.min(startIndex + itemsPerPage, filteredTransactions.length)} 条，
              共 {filteredTransactions.length} 条记录
            </div>

            <div className="flex items-center space-x-2">
              <motion.button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="flex items-center space-x-1 px-3 py-2 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                whileHover={{ scale: currentPage === 1 ? 1 : 1.02 }}
                whileTap={{ scale: currentPage === 1 ? 1 : 0.98 }}
              >
                <ChevronLeft size={16} />
                <span className="text-sm">上一页</span>
              </motion.button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <motion.button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200 ${
                      currentPage === page
                        ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/25'
                        : 'bg-white border border-slate-200 text-slate-600 hover:bg-slate-50'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {page}
                  </motion.button>
                ))}
              </div>

              <motion.button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="flex items-center space-x-1 px-3 py-2 bg-white border border-slate-200 rounded-lg hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                whileHover={{ scale: currentPage === totalPages ? 1 : 1.02 }}
                whileTap={{ scale: currentPage === totalPages ? 1 : 0.98 }}
              >
                <span className="text-sm">下一页</span>
                <ChevronRight size={16} />
              </motion.button>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};
