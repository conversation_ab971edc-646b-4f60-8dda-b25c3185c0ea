import { useState } from 'react';
import { AIApplication } from '../types';

const generateMockApplications = (): AIApplication[] => {
  const mockApps = [
    {
      id: '1',
      name: '智能客服助手',
      description: '基于大模型的智能客服系统，支持多轮对话和情感分析',
      avatar: '',
      isPublished: true,
      lastEdited: new Date('2024-01-15T10:30:00'),
    },
    {
      id: '2',
      name: '代码审查机器人',
      description: '自动化代码审查工具，提供代码质量分析和改进建议',
      avatar: '',
      isPublished: false,
      lastEdited: new Date('2024-01-14T16:45:00'),
    },
    {
      id: '3',
      name: '文档生成器',
      description: '智能文档生成工具，基于代码自动生成技术文档',
      avatar: '',
      isPublished: true,
      lastEdited: new Date('2024-01-13T09:15:00'),
    },
    {
      id: '4',
      name: '数据分析助手',
      description: '帮助用户分析数据，生成可视化图表和分析报告',
      avatar: '',
      isPublished: true,
      lastEdited: new Date('2024-01-12T14:20:00'),
    },
    {
      id: '5',
      name: '内容创作工具',
      description: 'AI驱动的内容创作平台，支持文章、博客和营销文案生成',
      avatar: '',
      isPublished: false,
      lastEdited: new Date('2024-01-11T11:00:00'),
    },
    {
      id: '6',
      name: '翻译助手',
      description: '多语言翻译工具，支持实时翻译和语言学习功能',
      avatar: '',
      isPublished: true,
      lastEdited: new Date('2024-01-10T13:30:00'),
    },
    {
      id: '7',
      name: '会议记录员',
      description: '自动记录会议内容，生成会议纪要和任务清单',
      avatar: '',
      isPublished: false,
      lastEdited: new Date('2024-01-09T08:45:00'),
    },
    {
      id: '8',
      name: '邮件智能分类',
      description: '智能邮件分类和优先级排序，提高邮件处理效率',
      avatar: '',
      isPublished: true,
      lastEdited: new Date('2024-01-08T15:10:00'),
    },
  ];

  return mockApps;
};

export const useApplications = () => {
  const [applications, setApplications] = useState<AIApplication[]>(generateMockApplications());

  const createApplication = (data: { name: string; description: string; avatar: string }): string => {
    const newApp: AIApplication = {
      id: Date.now().toString(),
      name: data.name,
      description: data.description,
      avatar: data.avatar,
      isPublished: false,
      lastEdited: new Date(),
    };

    setApplications((prev) => [newApp, ...prev]);
    return newApp.id;
  };

  return {
    applications,
    createApplication,
  };
};