/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go
//
// Generated by this command:
//
//	mockgen -destination ../../internal/mock/components/embedding/Embedding_mock.go --package embedding -source interface.go
//

// Package embedding is a generated GoMock package.
package embedding

import (
	context "context"
	reflect "reflect"

	embedding "github.com/cloudwego/eino/components/embedding"
	gomock "go.uber.org/mock/gomock"
)

// MockEmbedder is a mock of Embedder interface.
type MockEmbedder struct {
	ctrl     *gomock.Controller
	recorder *MockEmbedderMockRecorder
}

// MockEmbedderMockRecorder is the mock recorder for MockEmbedder.
type MockEmbedderMockRecorder struct {
	mock *MockEmbedder
}

// NewMockEmbedder creates a new mock instance.
func NewMockEmbedder(ctrl *gomock.Controller) *MockEmbedder {
	mock := &MockEmbedder{ctrl: ctrl}
	mock.recorder = &MockEmbedderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEmbedder) EXPECT() *MockEmbedderMockRecorder {
	return m.recorder
}

// EmbedStrings mocks base method.
func (m *MockEmbedder) EmbedStrings(ctx context.Context, texts []string, opts ...embedding.Option) ([][]float64, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, texts}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EmbedStrings", varargs...)
	ret0, _ := ret[0].([][]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EmbedStrings indicates an expected call of EmbedStrings.
func (mr *MockEmbedderMockRecorder) EmbedStrings(ctx, texts any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, texts}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EmbedStrings", reflect.TypeOf((*MockEmbedder)(nil).EmbedStrings), varargs...)
}
