import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Wallet, 
  CreditCard, 
  TrendingUp, 
  ArrowUpRight, 
  ArrowDownLeft,
  Zap,
  DollarSign,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';

export const WalletBalanceView: React.FC = () => {
  const [showBalance, setShowBalance] = useState(true);
  const [aiPoints] = useState(1250);
  const [incomeBalance] = useState(3680.50);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatPoints = (points: number) => {
    return new Intl.NumberFormat('zh-CN').format(points);
  };

  return (
    <motion.div 
      className="flex-1 p-8 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 min-h-screen"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.6 }}
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                钱包余额
              </h1>
              <p className="text-slate-600 mt-2">管理您的 AI 点数和收入余额</p>
            </div>
            
            <motion.button
              onClick={() => setShowBalance(!showBalance)}
              className="flex items-center space-x-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200/60 hover:bg-white transition-all duration-300 shadow-sm"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {showBalance ? <EyeOff size={18} /> : <Eye size={18} />}
              <span className="text-sm font-medium text-slate-700">
                {showBalance ? '隐藏余额' : '显示余额'}
              </span>
            </motion.button>
          </div>
        </motion.div>

        {/* Balance Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* AI Points Card */}
          <motion.div
            className="bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-3xl p-8 text-white shadow-2xl shadow-blue-500/25 relative overflow-hidden"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            whileHover={{ scale: 1.02, y: -5 }}
          >
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
            
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <Zap size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">AI 点数</h3>
                    <p className="text-blue-100 text-sm">用于购买平台服务</p>
                  </div>
                </div>
                <motion.div
                  className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <RefreshCw size={16} className="text-white" />
                </motion.div>
              </div>

              <div className="mb-6">
                <motion.div 
                  className="text-4xl font-bold mb-2"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                >
                  {showBalance ? formatPoints(aiPoints) : '****'}
                  <span className="text-xl ml-2 text-blue-100">点</span>
                </motion.div>
                <p className="text-blue-100 text-sm">
                  通过充值获得 • 不支持提现
                </p>
              </div>

              <motion.button
                className="w-full bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 flex items-center justify-center space-x-2 border border-white/20"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <CreditCard size={20} />
                <span>充值 AI 点数</span>
                <ArrowUpRight size={16} />
              </motion.button>
            </div>
          </motion.div>

          {/* Income Balance Card */}
          <motion.div
            className="bg-gradient-to-br from-emerald-500 via-emerald-600 to-teal-600 rounded-3xl p-8 text-white shadow-2xl shadow-emerald-500/25 relative overflow-hidden"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            whileHover={{ scale: 1.02, y: -5 }}
          >
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>
            
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <TrendingUp size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">收入余额</h3>
                    <p className="text-emerald-100 text-sm">智能体交易收益</p>
                  </div>
                </div>
                <motion.div
                  className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                >
                  <DollarSign size={16} className="text-white" />
                </motion.div>
              </div>

              <div className="mb-6">
                <motion.div 
                  className="text-4xl font-bold mb-2"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.6 }}
                >
                  {showBalance ? formatCurrency(incomeBalance) : '¥****.**'}
                </motion.div>
                <p className="text-emerald-100 text-sm">
                  可提现或转换为 AI 点数
                </p>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <motion.button
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2 border border-white/20"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <ArrowDownLeft size={16} />
                  <span className="text-sm">提现</span>
                </motion.button>
                
                <motion.button
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2 border border-white/20"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <RefreshCw size={16} />
                  <span className="text-sm">转换</span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          className="bg-white/80 backdrop-blur-sm rounded-3xl border border-slate-200/60 p-8 shadow-xl"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <h2 className="text-xl font-bold text-slate-900 mb-6">快捷操作</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <motion.div
              className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100 hover:border-blue-200 transition-all duration-300 cursor-pointer group"
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Wallet size={24} className="text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-slate-900">充值记录</h3>
                  <p className="text-slate-600 text-sm">查看充值历史</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="p-6 bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl border border-emerald-100 hover:border-emerald-200 transition-all duration-300 cursor-pointer group"
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-emerald-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp size={24} className="text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-slate-900">收益统计</h3>
                  <p className="text-slate-600 text-sm">查看收益详情</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl border border-purple-100 hover:border-purple-200 transition-all duration-300 cursor-pointer group"
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-purple-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <CreditCard size={24} className="text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-slate-900">支付设置</h3>
                  <p className="text-slate-600 text-sm">管理支付方式</p>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};
